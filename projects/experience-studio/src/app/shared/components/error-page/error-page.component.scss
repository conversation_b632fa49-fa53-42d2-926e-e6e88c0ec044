// Modern Error Page Styles with Glass Morphism Design
.error-page-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100%;
  padding: 2rem;
  background: var(--background-color, #ffffff);
  color: var(--body-text-color, #333);
  font-family: var(--font-family, 'Mulish', sans-serif);
  overflow: hidden;

  // Dark theme
  &.dark {
    background: var(--background-color, #121212);
    color: var(--body-text-color, #e0e0e0);
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem;
    min-height: 100vh;
  }
}

// Background floating elements
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-orb {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg,
    var(--main-title, linear-gradient(90deg, #6566cd 24%, #f63b8f 68%)),
    rgba(101, 102, 205, 0.3));
  filter: blur(40px);
  animation: float 6s ease-in-out infinite;

  &.orb-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.orb-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  &.orb-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 60%;
    animation-delay: 4s;
    background: linear-gradient(135deg,
      rgba(246, 59, 143, 0.4),
      rgba(101, 102, 205, 0.2));
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

// Main content card with glass morphism effect
.error-content-card {
  position: relative;
  z-index: 1;
  max-width: 600px;
  width: 100%;
  padding: 3rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: all 0.3s ease;

  .dark & {
    background: rgba(30, 30, 30, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 4px 16px rgba(0, 0, 0, 0.2);
  }

  @media (max-width: 768px) {
    padding: 2rem 1.5rem;
    border-radius: 20px;
  }

  @media (max-width: 480px) {
    padding: 1.5rem 1rem;
    border-radius: 16px;
  }
}

// Error Icon Section
.error-icon-section {
  margin-bottom: 2rem;

  @media (max-width: 480px) {
    margin-bottom: 1.5rem;
  }
}

.error-icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;

  @media (max-width: 480px) {
    margin-bottom: 1.5rem;
  }
}

.error-icon {
  width: 120px;
  height: 120px;
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    width: 100px;
    height: 100px;
  }

  @media (max-width: 480px) {
    width: 80px;
    height: 80px;
  }

  .error-circle {
    fill: none;
    stroke: var(--prompt-bar-border-color, #f96cab);
    stroke-width: 3;
    stroke-dasharray: 314;
    stroke-dashoffset: 314;
    animation: drawCircle 2s ease-out forwards;
  }

  .error-lines {
    fill: none;
    stroke: var(--body-text-color, #333);
    stroke-width: 3;
    stroke-linecap: round;
    opacity: 0;
    animation: fadeInLines 1s ease-out 1s forwards;
  }

  .error-dot {
    fill: var(--body-text-color, #333);
    transform-origin: center;
    transition: transform 0.3s ease;
  }

  .dark & {
    .error-lines,
    .error-dot {
      stroke: var(--body-text-color, #e0e0e0);
      fill: var(--body-text-color, #e0e0e0);
    }
  }
}

@keyframes drawCircle {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes fadeInLines {
  to {
    opacity: 1;
  }
}

// Interactive ERROR Letters
.error-letters-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;

  @media (max-width: 480px) {
    gap: 0.5rem;
  }
}

.error-letter {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-8px) scale(1.05);
  }

  &.hovered {
    transform: translateY(-8px) scale(1.05);
  }
}

.letter-main {
  font-size: 4rem;
  font-weight: 800;
  background: linear-gradient(135deg,
    var(--main-title, linear-gradient(90deg, #6566cd 24%, #f63b8f 68%)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    font-size: 3rem;
  }

  @media (max-width: 480px) {
    font-size: 2.5rem;
  }

  .dark & {
    background: linear-gradient(135deg, #a990ff, #f96cab);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

// Letter tooltip
.letter-tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(10px);
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;

  &.active {
    opacity: 1;
    transform: translateX(-50%) translateY(20px);
  }
}

.tooltip-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  white-space: nowrap;
  text-align: center;

  .dark & {
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
}

.tooltip-letter {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--prompt-bar-border-color, #f96cab);
  margin-bottom: 0.25rem;
}

.tooltip-text {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--body-text-color, #333);

  .dark & {
    color: var(--body-text-color, #e0e0e0);
  }
}

// Error Message Section
.error-message-section {
  margin: 3rem 0;

  @media (max-width: 480px) {
    margin: 2rem 0;
  }
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg,
    var(--main-title, linear-gradient(90deg, #6566cd 24%, #f63b8f 68%)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: 768px) {
    font-size: 2rem;
  }

  @media (max-width: 480px) {
    font-size: 1.75rem;
  }

  .dark & {
    background: linear-gradient(135deg, #a990ff, #f96cab);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.error-description {
  margin-bottom: 1.5rem;

  .animated-text {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--main-subtitle, #666d99);
    min-height: 3rem;

    @media (max-width: 480px) {
      font-size: 1rem;
      min-height: 2.5rem;
    }

    .dark & {
      color: var(--main-subtitle, #a0a0a0);
    }
  }
}

.cursor-blink {
  display: inline-block;
  animation: cursor-blink 1s infinite;
}

@keyframes cursor-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

// Progress Badge
.progress-badge-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.progress-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 50px;
  color: #dc3545;
  font-size: 0.875rem;
  font-weight: 500;
  animation: pulse-badge 2s infinite;

  .badge-icon {
    font-size: 1rem;
  }

  .dark & {
    background: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.4);
  }
}

@keyframes pulse-badge {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

// Action Section
.action-section {
  margin-top: 2rem;

  @media (max-width: 480px) {
    margin-top: 1.5rem;
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 0.75rem;
  }
}

// Modern Button Styles
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  user-select: none;

  @media (max-width: 480px) {
    padding: 1rem 1.5rem;
    font-size: 0.875rem;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  &:not(:disabled):hover {
    transform: translateY(-2px);
  }

  &:not(:disabled):active {
    transform: translateY(0);
  }
}

// Primary Button (Retry)
.btn-primary {
  background: linear-gradient(135deg,
    var(--prompt-bar-border-color, #f96cab),
    var(--prompt-bar-hover-color, #6566cd));
  color: white;
  box-shadow: 0 4px 15px rgba(249, 108, 171, 0.4);

  &:not(:disabled):hover {
    box-shadow: 0 6px 20px rgba(249, 108, 171, 0.6);
  }

  .dark & {
    box-shadow: 0 4px 15px rgba(249, 108, 171, 0.3);

    &:not(:disabled):hover {
      box-shadow: 0 6px 20px rgba(249, 108, 171, 0.5);
    }
  }
}

// Secondary Button (Home)
.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--body-text-color, #333);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  &:not(:disabled):hover {
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(200, 200, 200, 0.1);
    color: #999;

    &:hover {
      transform: none;
      background: rgba(200, 200, 200, 0.1);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
  }

  .dark & {
    background: rgba(30, 30, 30, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--body-text-color, #e0e0e0);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);

    &:not(:disabled):hover {
      background: rgba(30, 30, 30, 0.5);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    }

    &.disabled {
      background: rgba(60, 60, 60, 0.2);
      color: #777;

      &:hover {
        background: rgba(60, 60, 60, 0.2);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      }
    }
  }
}

// Button Icons
.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;

  svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
  }
}

.retry-icon {
  transition: transform 0.3s ease;

  &.spinning {
    animation: spin 1s linear infinite;
  }
}

.home-icon {
  transition: transform 0.3s ease;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Retry Counter Badge
.retry-counter-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4);
  animation: pulse-counter 2s infinite;
}

@keyframes pulse-counter {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

// Help Text
.help-text {
  text-align: center;

  p {
    font-size: 0.875rem;
    color: var(--main-subtitle, #666d99);
    margin: 0;
    opacity: 0.8;

    @media (max-width: 480px) {
      font-size: 0.8rem;
    }

    .dark & {
      color: var(--main-subtitle, #a0a0a0);
    }
  }
}

// Responsive Design Utilities
@media (max-width: 1024px) {
  .error-page-container {
    padding: 1.5rem;
  }

  .error-content-card {
    max-width: 500px;
  }
}

@media (max-width: 768px) {
  .error-page-container {
    padding: 1rem;
  }

  .error-content-card {
    padding: 2rem 1.5rem;
    max-width: 400px;
  }

  .floating-orb {
    &.orb-1 {
      width: 150px;
      height: 150px;
    }

    &.orb-2 {
      width: 100px;
      height: 100px;
    }

    &.orb-3 {
      width: 80px;
      height: 80px;
    }
  }
}

@media (max-width: 480px) {
  .error-page-container {
    padding: 0.75rem;
  }

  .error-content-card {
    padding: 1.5rem 1rem;
    border-radius: 16px;
  }

  .floating-orb {
    filter: blur(30px);

    &.orb-1 {
      width: 120px;
      height: 120px;
    }

    &.orb-2 {
      width: 80px;
      height: 80px;
    }

    &.orb-3 {
      width: 60px;
      height: 60px;
    }
  }
}

// Accessibility and Performance Optimizations
.error-page-container {
  // Reduce motion for users who prefer it
  @media (prefers-reduced-motion: reduce) {
    .floating-orb,
    .error-icon,
    .letter-main,
    .btn {
      animation: none !important;
      transition: none !important;
    }
  }

  // High contrast mode support
  @media (prefers-contrast: high) {
    .error-content-card {
      border: 2px solid var(--body-text-color);
      background: var(--background-color);
    }

    .btn-primary {
      background: var(--body-text-color);
      color: var(--background-color);
    }

    .btn-secondary {
      background: transparent;
      border: 2px solid var(--body-text-color);
      color: var(--body-text-color);
    }
  }
}

// Focus states for accessibility
.error-letter,
.btn {
  &:focus-visible {
    outline: 2px solid var(--prompt-bar-border-color, #f96cab);
    outline-offset: 2px;
  }
}

// Print styles
@media print {
  .error-page-container {
    background: white !important;
    color: black !important;

    .floating-orb {
      display: none;
    }

    .error-content-card {
      background: white !important;
      border: 1px solid black !important;
      box-shadow: none !important;
    }

    .btn {
      border: 1px solid black !important;
      background: white !important;
      color: black !important;
    }
  }
}
