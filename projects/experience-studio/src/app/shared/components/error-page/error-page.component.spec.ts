import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { ErrorPageComponent } from './error-page.component';

describe('ErrorPageComponent', () => {
  let component: ErrorPageComponent;
  let fixture: ComponentFixture<ErrorPageComponent>;
  let debugElement: DebugElement;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ErrorPageComponent,
        BrowserAnimationsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ErrorPageComponent);
    component = fixture.componentInstance;
    debugElement = fixture.debugElement;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.errorDescription).toBe('');
    expect(component.theme).toBe('light');
    expect(component.progressState).toBe('');
    expect(component.isRetrying).toBe(false);
  });

  it('should initialize error letters on ngOnInit', () => {
    component.ngOnInit();
    const letters = component.errorLetters$.value;
    
    expect(letters).toHaveLength(5);
    expect(letters.map(l => l.char)).toEqual(['E', 'R', 'R', 'O', 'R']);
    expect(letters.every(l => l.state === 'normal')).toBe(true);
  });

  it('should start typing animation on ngOnInit', () => {
    component.errorDescription = 'Test error message';
    component.ngOnInit();
    
    // Wait for typing animation to start
    setTimeout(() => {
      expect(component.animatedDescription$.value.length).toBeGreaterThan(0);
    }, 100);
  });

  it('should handle letter hover interactions', () => {
    component.ngOnInit();
    
    component.onLetterMouseEnter(0);
    expect(component.errorLetters$.value[0].state).toBe('hovered');
    
    component.onLetterMouseLeave(0);
    expect(component.errorLetters$.value[0].state).toBe('normal');
  });

  it('should increment retry count on retry click', () => {
    component.onRetryClick();
    expect(component.retryCount$.value).toBe(1);
  });

  it('should enable home button after 3 retries', () => {
    expect(component.homeButtonEnabled$.value).toBe(false);
    
    // Retry 3 times
    component.onRetryClick();
    component.onRetryClick();
    component.onRetryClick();
    
    expect(component.homeButtonEnabled$.value).toBe(true);
  });

  it('should emit retry event on retry click', () => {
    spyOn(component.retry, 'emit');
    
    component.onRetryClick();
    
    // Wait for animation delay
    setTimeout(() => {
      expect(component.retry.emit).toHaveBeenCalled();
    }, 600);
  });

  it('should emit goHome event when home button is enabled', () => {
    spyOn(component.goHome, 'emit');
    component.homeButtonEnabled$.next(true);
    
    component.onGoHomeClick();
    expect(component.goHome.emit).toHaveBeenCalled();
  });

  it('should not emit goHome event when home button is disabled', () => {
    spyOn(component.goHome, 'emit');
    component.homeButtonEnabled$.next(false);
    
    component.onGoHomeClick();
    expect(component.goHome.emit).not.toHaveBeenCalled();
  });

  it('should return correct letter descriptions', () => {
    expect(component.getLetterDescription(0)).toBe('Execution Halt');
    expect(component.getLetterDescription(1)).toBe('Request Issue');
    expect(component.getLetterDescription(2)).toBe('Result: Failure');
    expect(component.getLetterDescription(3)).toBe('Operation Blocked');
    expect(component.getLetterDescription(4)).toBe('Retry/Report');
  });

  it('should return correct home button tooltip', () => {
    component.homeButtonEnabled$.next(false);
    component.retryCount$.next(1);
    
    expect(component.getHomeButtonTooltip()).toBe('Retry 2 more times to enable');
    
    component.homeButtonEnabled$.next(true);
    expect(component.getHomeButtonTooltip()).toBe('Go Home');
  });

  it('should render error letters in template', () => {
    component.ngOnInit();
    fixture.detectChanges();
    
    const letterElements = debugElement.queryAll(By.css('.error-letter'));
    expect(letterElements).toHaveLength(5);
  });

  it('should render action buttons', () => {
    fixture.detectChanges();
    
    const retryButton = debugElement.query(By.css('.retry-btn'));
    const homeButton = debugElement.query(By.css('.home-btn'));
    
    expect(retryButton).toBeTruthy();
    expect(homeButton).toBeTruthy();
  });

  it('should apply theme classes correctly', () => {
    component.theme = 'dark';
    fixture.detectChanges();
    
    const container = debugElement.query(By.css('.error-page-container'));
    expect(container.nativeElement.classList).toContain('dark');
  });

  it('should display progress badge when progressState is set', () => {
    component.progressState = 'Loading...';
    fixture.detectChanges();
    
    const progressBadge = debugElement.query(By.css('.progress-badge'));
    expect(progressBadge).toBeTruthy();
  });

  it('should clean up intervals on destroy', () => {
    component.ngOnInit();
    spyOn(window, 'clearInterval');
    
    component.ngOnDestroy();
    
    expect(window.clearInterval).toHaveBeenCalled();
  });

  it('should handle typing animation correctly', () => {
    component.errorDescription = 'Test';
    component.startTypingAnimation();
    
    setTimeout(() => {
      expect(component.animatedDescription$.value).toBe('Test');
    }, 200);
  });

  it('should celebrate when home button is enabled', () => {
    component.ngOnInit();
    spyOn(component, 'celebrateHomeButtonEnabled');
    
    // Trigger home button enabling
    component.retryCount$.next(2);
    component.onRetryClick();
    
    expect(component.celebrateHomeButtonEnabled).toHaveBeenCalled();
  });
});
