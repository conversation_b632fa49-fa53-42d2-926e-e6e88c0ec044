import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ErrorPageComponent } from './error-page.component';

@Component({
  selector: 'app-error-page-demo',
  standalone: true,
  imports: [CommonModule, FormsModule, ErrorPageComponent],
  template: `
    <div class="demo-container">
      <div class="demo-controls">
        <h2>Error Page Demo</h2>
        <div class="control-group">
          <label>
            Theme:
            <select [(ngModel)]="selectedTheme" (change)="updateTheme()">
              <option value="light">Light</option>
              <option value="dark">Dark</option>
            </select>
          </label>
        </div>
        <div class="control-group">
          <label>
            Error Message:
            <input
              type="text"
              [(ngModel)]="errorMessage"
              placeholder="Enter error message"
              (input)="updateErrorMessage()"
            />
          </label>
        </div>
        <div class="control-group">
          <label>
            Progress State:
            <input
              type="text"
              [(ngModel)]="progressState"
              placeholder="e.g., Loading..."
            />
          </label>
        </div>
        <div class="control-group">
          <button (click)="simulateError()">Simulate Different Error</button>
          <button (click)="resetDemo()">Reset Demo</button>
        </div>
      </div>

      <div class="demo-preview" [ngClass]="selectedTheme + '-theme'">
        <app-error-page
          [errorDescription]="errorMessage"
          [theme]="selectedTheme"
          [progressState]="progressState"
          (retry)="onRetry()"
          (goHome)="onGoHome()"
          (showDetails)="onShowDetails()">
        </app-error-page>
      </div>

      <div class="demo-events">
        <h3>Event Log:</h3>
        <div class="event-log">
          <div *ngFor="let event of eventLog" class="event-item">
            <span class="event-time">{{ event.time }}</span>
            <span class="event-type">{{ event.type }}</span>
            <span class="event-message">{{ event.message }}</span>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .demo-container {
      display: grid;
      grid-template-columns: 300px 1fr;
      grid-template-rows: 1fr auto;
      height: 100vh;
      gap: 1rem;
      padding: 1rem;
      background: var(--background-color, #f8f9fa);
    }

    .demo-controls {
      background: white;
      padding: 1.5rem;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      height: fit-content;
    }

    .demo-controls h2 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.25rem;
    }

    .control-group {
      margin-bottom: 1rem;
    }

    .control-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #555;
    }

    .control-group input,
    .control-group select {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 0.875rem;
    }

    .control-group button {
      padding: 0.5rem 1rem;
      margin-right: 0.5rem;
      margin-bottom: 0.5rem;
      border: none;
      border-radius: 6px;
      background: #6566cd;
      color: white;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background 0.2s ease;
    }

    .control-group button:hover {
      background: #5555bd;
    }

    .demo-preview {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      position: relative;
    }

    .demo-preview.dark-theme {
      background: #1a1a1a;
    }

    .demo-events {
      grid-column: 1 / -1;
      background: white;
      padding: 1rem;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      max-height: 200px;
      overflow-y: auto;
    }

    .demo-events h3 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1rem;
    }

    .event-log {
      font-family: 'Courier New', monospace;
      font-size: 0.8rem;
    }

    .event-item {
      display: grid;
      grid-template-columns: 80px 80px 1fr;
      gap: 0.5rem;
      padding: 0.25rem 0;
      border-bottom: 1px solid #eee;
    }

    .event-time {
      color: #666;
    }

    .event-type {
      color: #6566cd;
      font-weight: 500;
    }

    .event-message {
      color: #333;
    }

    @media (max-width: 768px) {
      .demo-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
      }
    }
  `]
})
export class ErrorPageDemoComponent {
  selectedTheme: 'light' | 'dark' = 'light';
  errorMessage = 'Something went wrong while processing your request. Please try again or contact support if the problem persists.';
  progressState = '';

  eventLog: Array<{time: string, type: string, message: string}> = [];

  private errorMessages = [
    'Network connection failed. Please check your internet connection and try again.',
    'Server is temporarily unavailable. Our team has been notified and is working on a fix.',
    'Invalid request format. Please refresh the page and try again.',
    'Authentication failed. Please log in again to continue.',
    'File upload failed. Please ensure your file is under 10MB and try again.',
    'Database connection timeout. Please try again in a few moments.',
  ];

  updateTheme() {
    this.logEvent('THEME', `Changed to ${this.selectedTheme} theme`);
  }

  updateErrorMessage() {
    this.logEvent('MESSAGE', 'Error message updated');
  }

  simulateError() {
    const randomMessage = this.errorMessages[Math.floor(Math.random() * this.errorMessages.length)];
    this.errorMessage = randomMessage;
    this.progressState = Math.random() > 0.5 ? 'Processing...' : '';
    this.logEvent('SIMULATE', 'New error simulated');
  }

  resetDemo() {
    this.selectedTheme = 'light';
    this.errorMessage = 'Something went wrong while processing your request. Please try again or contact support if the problem persists.';
    this.progressState = '';
    this.eventLog = [];
    this.logEvent('RESET', 'Demo reset to defaults');
  }

  onRetry() {
    this.logEvent('RETRY', 'Retry button clicked');
  }

  onGoHome() {
    this.logEvent('HOME', 'Go Home button clicked');
  }

  onShowDetails() {
    this.logEvent('DETAILS', 'Show Details button clicked');
  }

  private logEvent(type: string, message: string) {
    const time = new Date().toLocaleTimeString();
    this.eventLog.unshift({ time, type, message });

    // Keep only last 20 events
    if (this.eventLog.length > 20) {
      this.eventLog = this.eventLog.slice(0, 20);
    }
  }
}
