<!-- Modern Error Page with Glass Morphism Design -->
<div class="error-page-container" [ngClass]="theme" [@fadeInUp]>
  <!-- Background Elements -->
  <div class="background-elements">
    <div class="floating-orb orb-1" [@pulseAnimation]="pulseState$ | async"></div>
    <div class="floating-orb orb-2" [@rotateAnimation]="rotateState$ | async"></div>
    <div class="floating-orb orb-3"></div>
  </div>

  <!-- Main Content Card -->
  <div class="error-content-card" [@fadeInUp]>
    <!-- Error Icon Section -->
    <div class="error-icon-section" [@errorTextAnimation]>
      <div class="error-icon-container">
        <svg class="error-icon" viewBox="0 0 120 120" [@robotShake]="robotState$ | async">
          <circle cx="60" cy="60" r="50" class="error-circle"/>
          <path d="M40 45 L80 45 M40 75 L80 75" class="error-lines"/>
          <circle cx="45" cy="45" r="3" class="error-dot" [@eyesBlink]="eyesState$ | async"/>
          <circle cx="75" cy="45" r="3" class="error-dot" [@eyesBlink]="eyesState$ | async"/>
        </svg>
      </div>

      <!-- Interactive ERROR Letters -->
      <div class="error-letters-container">
        <div class="error-letter"
             *ngFor="let letter of errorLetters$ | async; let i = index"
             (mouseenter)="onLetterMouseEnter(i)"
             (mouseleave)="onLetterMouseLeave(i)"
             [ngClass]="{'hovered': letter.state === 'hovered'}">
          <span class="letter-main">{{ letter.char }}</span>
          <div class="letter-tooltip" [ngClass]="{'active': letter.state === 'hovered'}">
            <div class="tooltip-content">
              <span class="tooltip-letter">{{ letter.char }}</span>
              <span class="tooltip-text">{{ getLetterDescription(i) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Message Section -->
    <div class="error-message-section" [@fadeInUp]>
      <h2 class="error-title">Something went wrong</h2>
      <div class="error-description">
        <p class="animated-text">
          {{ animatedDescription$ | async }}
          <span class="cursor-blink">|</span>
        </p>
      </div>

      <!-- Progress State Badge -->
      <div class="progress-badge-container" *ngIf="progressState">
        <div class="progress-badge">
          <span class="badge-icon">⚠️</span>
          <span class="badge-text">{{ progressState }}</span>
        </div>
      </div>
    </div>

    <!-- Action Buttons Section -->
    <div class="action-section" [@fadeInUp]>
      <div class="action-buttons">
        <button class="btn btn-primary retry-btn"
                (click)="onRetryClick()"
                [disabled]="isRetrying">
          <span class="btn-icon">
            <svg viewBox="0 0 24 24" class="retry-icon" [ngClass]="{'spinning': isRetrying}">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
            </svg>
          </span>
          <span class="btn-text">{{ isRetrying ? 'Retrying...' : 'Try Again' }}</span>
        </button>

        <button class="btn btn-secondary home-btn"
                [ngClass]="{'disabled': !(homeButtonEnabled$ | async)}"
                (click)="onGoHomeClick()"
                [title]="getHomeButtonTooltip()">
          <span class="btn-icon">
            <svg viewBox="0 0 24 24" class="home-icon">
              <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
            </svg>
          </span>
          <span class="btn-text">Go Home</span>

          <!-- Retry Counter Badge -->
          <div class="retry-counter-badge"
               *ngIf="!(homeButtonEnabled$ | async) && (retryCount$ | async)! > 0">
            {{ retryCount$ | async }}/3
          </div>
        </button>
      </div>

      <!-- Help Text -->
      <div class="help-text">
        <p *ngIf="!(homeButtonEnabled$ | async)">
          Try {{ 3 - (retryCount$ | async)! }} more times to unlock the home button
        </p>
        <p *ngIf="(homeButtonEnabled$ | async)">
          You can now return to the home page
        </p>
      </div>
    </div>
  </div>
</div>

